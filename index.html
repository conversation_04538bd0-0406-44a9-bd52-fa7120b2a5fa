<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>قريباً - أريج نينوى للتجارة العامة محدودة المسؤولية</title>
    <meta
      name="description"
      content="أريج نينوى للتجارة العامة - وكلاء معتمدون لسيارات كيا في العراق. أحدث موديلات كيا 2024-2025، خدمات صيانة، قطع غيار أصلية، وعروض تمويل مرنة."
    />
    <meta
      name="keywords"
      content="أريج نينوى, كيا, سيارات كيا, وكيل كيا العراق, سيارات جديدة, صيانة سيارات, قطع غيار كيا, تمويل سيارات, نينوى, بغداد"
    />
    <meta name="author" content="أريج نينوى للتجارة العامة محدودة المسؤولية" />
    <meta
      property="og:title"
      content="أريج نينوى - للتجارة العامة محدودة المسؤولية"
    />
    <meta
      property="og:description"
      content="وكلاء معتمدون لسيارات كيا في العراق. أحدث الموديلات، خدمات صيانة متميزة، وعروض تمويل مرنة."
    />
    <meta property="og:image" content="logo.png" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <link rel="icon" type="image/png" href="logo.png" />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Cairo', sans-serif;
        background: linear-gradient(
          135deg,
          rgba(5, 20, 31, 0.95) 0%,
          rgba(0, 0, 0, 0.9) 100%
        );
        min-height: 100vh;
        overflow-x: hidden;
        position: relative;
      }

      /* خلفية الصورة */
      .background-image {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -2;
        background-image: url('background.webp');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;
      }

      /* خلفية متحركة */
      .animated-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: linear-gradient(
          135deg,
          rgba(5, 20, 31, 0.85) 0%,
          rgba(0, 0, 0, 0.8) 50%,
          rgba(5, 20, 31, 0.9) 100%
        );
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
      }

      .particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
      }

      .particle {
        position: absolute;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
      }

      @keyframes gradientShift {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        50% {
          transform: translateY(-20px) rotate(180deg);
        }
      }

      .container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
        position: relative;
        z-index: 1;
      }

      .glass-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(25px);
        border-radius: 35px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        padding: 70px 50px;
        text-align: center;
        box-shadow: 0 30px 60px rgba(5, 20, 31, 0.4),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        max-width: 900px;
        width: 100%;
        animation: slideUp 1s ease-out;
        position: relative;
        overflow: hidden;
      }

      .glass-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        animation: shimmer 3s infinite;
      }

      @keyframes shimmer {
        0% {
          left: -100%;
        }
        100% {
          left: 100%;
        }
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(50px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .logo {
        width: 180px;
        height: 120px;
        margin: 0 auto 30px;
        overflow: hidden;
        border: 4px solid #ffffff;
        animation: pulse 2s ease-in-out infinite;
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
      }

      .logo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      .title {
        font-size: 3.5rem;
        font-weight: 900;
        color: #ffffff;
        margin-bottom: 15px;
        text-shadow: 2px 2px 4px rgba(5, 20, 31, 0.8);
        animation: glow 2s ease-in-out infinite alternate;
        position: relative;
        z-index: 2;
      }

      .company-name {
        font-size: 1.8rem;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 25px;
        text-shadow: 1px 1px 3px rgba(5, 20, 31, 0.8);
        line-height: 1.4;
        position: relative;
        z-index: 2;
        opacity: 0.9;
      }

      @keyframes glow {
        from {
          text-shadow: 2px 2px 4px rgba(5, 20, 31, 0.8),
            0 0 20px rgba(255, 255, 255, 0.3);
        }
        to {
          text-shadow: 2px 2px 4px rgba(5, 20, 31, 0.8),
            0 0 30px rgba(255, 255, 255, 0.6);
        }
      }

      .subtitle {
        font-size: 1.5rem;
        color: #ffffff;
        margin-bottom: 40px;
        font-weight: 400;
        opacity: 0.9;
      }

      .countdown-container {
        margin: 40px 0;
      }

      .countdown-title {
        font-size: 1.8rem;
        color: #ffffff;
        margin-bottom: 30px;
        font-weight: 600;
      }

      .countdown {
        display: flex;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
        margin-bottom: 40px;
      }

      .time-unit {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 25px 20px;
        min-width: 120px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
        animation: bounceIn 1s ease-out;
      }

      .time-unit:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.15);
        border-color: #ffffff;
      }

      @keyframes bounceIn {
        0% {
          opacity: 0;
          transform: scale(0.3);
        }
        50% {
          opacity: 1;
          transform: scale(1.05);
        }
        70% {
          transform: scale(0.9);
        }
        100% {
          opacity: 1;
          transform: scale(1);
        }
      }

      .time-number {
        font-size: 3rem;
        font-weight: 900;
        color: #ffffff;
        display: block;
        line-height: 1;
      }

      .time-label {
        font-size: 1rem;
        color: #ffffff;
        margin-top: 10px;
        font-weight: 500;
        opacity: 0.8;
      }

      .description {
        font-size: 1.2rem;
        color: #ffffff;
        line-height: 1.8;
        margin-bottom: 40px;
        opacity: 0.9;
      }

      .social-links {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 30px;
      }

      .social-link {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        color: #ffffff;
        font-size: 1.5rem;
        text-decoration: none;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .social-link:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-3px) scale(1.1);
        border-color: #ffffff;
      }

      .notify-form {
        margin-top: 40px;
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
      }

      .email-input {
        padding: 15px 25px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50px;
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        font-size: 1rem;
        min-width: 300px;
        outline: none;
        transition: all 0.3s ease;
      }

      .email-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
      }

      .email-input:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: #ffffff;
        transform: scale(1.02);
      }

      .notify-btn {
        padding: 15px 30px;
        border: 2px solid #ffffff;
        border-radius: 50px;
        background: rgba(5, 20, 31, 0.8);
        color: #ffffff;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 10px 20px rgba(5, 20, 31, 0.5);
      }

      .notify-btn:hover {
        transform: translateY(-2px);
        background: #05141f;
        box-shadow: 0 15px 30px rgba(5, 20, 31, 0.7);
      }

      /* قسم موديلات كيا */
      .kia-models {
        margin: 40px 0;
      }

      .models-title {
        font-size: 1.8rem;
        color: #ffffff;
        text-align: center;
        margin-bottom: 30px;
        font-weight: 600;
      }

      .models-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      .model-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }

      .model-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.15);
        border-color: #ffffff;
      }

      .model-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #ffffff;
        margin-bottom: 8px;
      }

      .model-desc {
        font-size: 0.9rem;
        color: #ffffff;
        opacity: 0.8;
      }

      @media (max-width: 768px) {
        .glass-card {
          padding: 40px 20px;
          margin: 20px;
        }

        .title {
          font-size: 2.5rem;
        }

        .company-name {
          font-size: 1.4rem;
        }

        .countdown {
          gap: 15px;
        }

        .time-unit {
          min-width: 80px;
          padding: 20px 15px;
        }

        .time-number {
          font-size: 2rem;
        }

        .email-input {
          min-width: 250px;
        }

        .models-grid {
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 15px;
        }

        .models-title {
          font-size: 1.5rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="background-image"></div>
    <div class="animated-bg"></div>
    <div class="particles" id="particles"></div>

    <div class="container">
      <div class="glass-card">
        <div class="logo">
          <img
            src="logo.png"
            alt="أريج نينوى"
            onerror="this.style.display='none'"
          />
        </div>

        <h1 class="title">أريج نينوى</h1>
        <h2 class="company-name">للتجارة العامة محدودة المسؤولية</h2>
        <p class="subtitle">قادمة قريباً</p>

        <div class="countdown-container">
          <h2 class="countdown-title">العد التنازلي للإطلاق</h2>
          <div class="countdown" id="countdown">
            <div class="time-unit">
              <span class="time-number" id="days">20</span>
              <span class="time-label">يوم</span>
            </div>
            <div class="time-unit">
              <span class="time-number" id="hours">00</span>
              <span class="time-label">ساعة</span>
            </div>
            <div class="time-unit">
              <span class="time-number" id="minutes">00</span>
              <span class="time-label">دقيقة</span>
            </div>
            <div class="time-unit">
              <span class="time-number" id="seconds">00</span>
              <span class="time-label">ثانية</span>
            </div>
          </div>
        </div>

        <p class="description">
          نحن في <strong>أريج نينوى للتجارة العامة</strong> متخصصون في بيع
          سيارات <strong>كيا</strong> في العراق. <br /><br />
          موقعنا الجديد سيوفر لكم:
          <br />
          🚗 أحدث موديلات سيارات كيا 2025-2026
          <br />
          🔧 خدمات صيانة وقطع غيار أصلية
          <br />
          💰 أسعار تنافسية وعروض تمويل مرنة
          <br />
          �️ ضمان شامل وخدمة ما بعد البيع
          <br />
          � معارض حديثة في نينوى وبغداد
          <br />
          👨‍� فريق مختص لتقديم الاستشارة المهنية
          <br /><br />
          <strong>كيا... حركة ملهمة</strong>
          <br />
          ترقبوا افتتاح موقعنا الإلكتروني قريباً!
        </p>

        <div class="kia-models">
          <h3 class="models-title">موديلات كيا المتوفرة قريباً</h3>
          <div class="models-grid">
            <div class="model-card">
              <div class="model-name">كيا سيراتو</div>
              <div class="model-desc">سيدان أنيقة وعملية</div>
            </div>
            <div class="model-card">
              <div class="model-name">كيا سبورتاج</div>
              <div class="model-desc">SUV متطورة وقوية</div>
            </div>
            <div class="model-card">
              <div class="model-name">كيا كي 5</div>
              <div class="model-desc">سيدان رياضية متطورة</div>
            </div>
            <div class="model-card">
              <div class="model-name">كيا سورينتو</div>
              <div class="model-desc">SUV فاخرة للعائلة</div>
            </div>
          </div>
        </div>

        <div class="notify-form">
          <input
            type="email"
            class="email-input"
            placeholder="أدخل بريدك الإلكتروني للحصول على إشعار"
          />
          <button class="notify-btn" onclick="subscribeEmail()">
            <i class="fas fa-bell"></i> أشعرني
          </button>
        </div>

        <div class="social-links">
          <a href="#" class="social-link" title="فيسبوك">
            <i class="fab fa-facebook-f"></i>
          </a>
          <a href="#" class="social-link" title="تويتر">
            <i class="fab fa-twitter"></i>
          </a>
          <a href="#" class="social-link" title="إنستغرام">
            <i class="fab fa-instagram"></i>
          </a>
          <a href="#" class="social-link" title="لينكد إن">
            <i class="fab fa-linkedin-in"></i>
          </a>
        </div>
      </div>
    </div>

    <script>
      // إنشاء الجسيمات المتحركة
      function createParticles() {
        const particlesContainer = document.getElementById('particles');
        const particleCount = 50;

        for (let i = 0; i < particleCount; i++) {
          const particle = document.createElement('div');
          particle.className = 'particle';

          const size = Math.random() * 5 + 2;
          const x = Math.random() * window.innerWidth;
          const y = Math.random() * window.innerHeight;
          const delay = Math.random() * 6;

          particle.style.width = size + 'px';
          particle.style.height = size + 'px';
          particle.style.left = x + 'px';
          particle.style.top = y + 'px';
          particle.style.animationDelay = delay + 's';

          particlesContainer.appendChild(particle);
        }
      }

      // العد التنازلي
      function startCountdown() {
        const launchDate = new Date();
        launchDate.setDate(launchDate.getDate() + 20);

        function updateCountdown() {
          const now = new Date().getTime();
          const distance = launchDate.getTime() - now;

          if (distance < 0) {
            document.getElementById('countdown').innerHTML =
              '<h2 style="color: white;">تم الإطلاق!</h2>';
            return;
          }

          const days = Math.floor(distance / (1000 * 60 * 60 * 24));
          const hours = Math.floor(
            (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
          );
          const minutes = Math.floor(
            (distance % (1000 * 60 * 60)) / (1000 * 60)
          );
          const seconds = Math.floor((distance % (1000 * 60)) / 1000);

          document.getElementById('days').textContent = days
            .toString()
            .padStart(2, '0');
          document.getElementById('hours').textContent = hours
            .toString()
            .padStart(2, '0');
          document.getElementById('minutes').textContent = minutes
            .toString()
            .padStart(2, '0');
          document.getElementById('seconds').textContent = seconds
            .toString()
            .padStart(2, '0');
        }

        updateCountdown();
        setInterval(updateCountdown, 1000);
      }

      // اشتراك البريد الإلكتروني
      function subscribeEmail() {
        const email = document.querySelector('.email-input').value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && emailRegex.test(email)) {
          // تأثير بصري للنجاح
          const btn = document.querySelector('.notify-btn');
          const originalText = btn.innerHTML;
          btn.innerHTML = '<i class="fas fa-check"></i> تم الاشتراك!';
          btn.style.background = '#ffffff';
          btn.style.color = '#05141f';

          setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = 'rgba(5, 20, 31, 0.8)';
            btn.style.color = '#ffffff';
          }, 3000);

          document.querySelector('.email-input').value = '';

          // حفظ البريد الإلكتروني في localStorage
          const subscribers = JSON.parse(
            localStorage.getItem('subscribers') || '[]'
          );
          if (!subscribers.includes(email)) {
            subscribers.push(email);
            localStorage.setItem('subscribers', JSON.stringify(subscribers));
          }
        } else {
          // تأثير بصري للخطأ
          const input = document.querySelector('.email-input');
          input.style.border = '2px solid #ffffff';
          input.style.background = 'rgba(255, 255, 255, 0.2)';
          input.placeholder = 'يرجى إدخال بريد إلكتروني صحيح';

          setTimeout(() => {
            input.style.border = '2px solid rgba(255, 255, 255, 0.3)';
            input.style.background = 'rgba(255, 255, 255, 0.1)';
            input.placeholder = 'أدخل بريدك الإلكتروني للحصول على إشعار';
          }, 3000);
        }
      }

      // تأثير الكتابة المتحركة للعنوان
      function animateTitle() {
        const title = document.querySelector('.title');
        const companyName = document.querySelector('.company-name');

        title.style.opacity = '0';
        companyName.style.opacity = '0';

        setTimeout(() => {
          title.style.transition = 'opacity 1s ease-in-out';
          title.style.opacity = '1';
        }, 500);

        setTimeout(() => {
          companyName.style.transition = 'opacity 1s ease-in-out';
          companyName.style.opacity = '1';
        }, 1500);
      }

      // تشغيل الوظائف عند تحميل الصفحة
      window.addEventListener('load', function () {
        createParticles();
        startCountdown();
        animateTitle();

        // إضافة تأثير التمرير السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
          anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
              behavior: 'smooth',
            });
          });
        });
      });

      // إعادة إنشاء الجسيمات عند تغيير حجم النافذة
      window.addEventListener('resize', function () {
        document.getElementById('particles').innerHTML = '';
        createParticles();
      });
    </script>
  </body>
</html>
