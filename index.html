<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قريباً - موقع أريج نينوى</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية متحركة */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            position: relative;
            z-index: 1;
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 60px 40px;
            text-align: center;
            box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 100%;
            animation: slideUp 1s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            border-radius: 50%;
            overflow: hidden;
            border: 4px solid rgba(255, 255, 255, 0.3);
            animation: pulse 2s ease-in-out infinite;
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .title {
            font-size: 3.5rem;
            font-weight: 900;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.3); }
            to { text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 255, 255, 0.5); }
        }

        .subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 40px;
            font-weight: 400;
        }

        .countdown-container {
            margin: 40px 0;
        }

        .countdown-title {
            font-size: 1.8rem;
            color: white;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .countdown {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-bottom: 40px;
        }

        .time-unit {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 25px 20px;
            min-width: 120px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            animation: bounceIn 1s ease-out;
        }

        .time-unit:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }

        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3); }
            50% { opacity: 1; transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { opacity: 1; transform: scale(1); }
        }

        .time-number {
            font-size: 3rem;
            font-weight: 900;
            color: white;
            display: block;
            line-height: 1;
        }

        .time-label {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 10px;
            font-weight: 500;
        }

        .description {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.8;
            margin-bottom: 40px;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            font-size: 1.5rem;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-3px) scale(1.1);
        }

        .notify-form {
            margin-top: 40px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .email-input {
            padding: 15px 25px;
            border: none;
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1rem;
            min-width: 300px;
            outline: none;
            transition: all 0.3s ease;
        }

        .email-input:focus {
            background: white;
            transform: scale(1.02);
        }

        .notify-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(238, 90, 36, 0.3);
        }

        .notify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(238, 90, 36, 0.4);
        }

        @media (max-width: 768px) {
            .glass-card {
                padding: 40px 20px;
                margin: 20px;
            }
            
            .title {
                font-size: 2.5rem;
            }
            
            .countdown {
                gap: 15px;
            }
            
            .time-unit {
                min-width: 80px;
                padding: 20px 15px;
            }
            
            .time-number {
                font-size: 2rem;
            }
            
            .email-input {
                min-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    <div class="particles" id="particles"></div>
    
    <div class="container">
        <div class="glass-card">
            <div class="logo">
                <img src="logo.png" alt="أريج نينوى" onerror="this.style.display='none'">
            </div>
            
            <h1 class="title">أريج نينوى</h1>
            <p class="subtitle">موقع رائع قادم قريباً</p>
            
            <div class="countdown-container">
                <h2 class="countdown-title">العد التنازلي للإطلاق</h2>
                <div class="countdown" id="countdown">
                    <div class="time-unit">
                        <span class="time-number" id="days">20</span>
                        <span class="time-label">يوم</span>
                    </div>
                    <div class="time-unit">
                        <span class="time-number" id="hours">00</span>
                        <span class="time-label">ساعة</span>
                    </div>
                    <div class="time-unit">
                        <span class="time-number" id="minutes">00</span>
                        <span class="time-label">دقيقة</span>
                    </div>
                    <div class="time-unit">
                        <span class="time-number" id="seconds">00</span>
                        <span class="time-label">ثانية</span>
                    </div>
                </div>
            </div>
            
            <p class="description">
                نحن نعمل بجد لنقدم لكم تجربة استثنائية ومميزة. 
                موقعنا الجديد سيحمل لكم محتوى رائع وخدمات متطورة.
                ترقبوا الإطلاق قريباً!
            </p>
            
            <div class="notify-form">
                <input type="email" class="email-input" placeholder="أدخل بريدك الإلكتروني للحصول على إشعار">
                <button class="notify-btn" onclick="subscribeEmail()">
                    <i class="fas fa-bell"></i> أشعرني
                </button>
            </div>
            
            <div class="social-links">
                <a href="#" class="social-link" title="فيسبوك">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <a href="#" class="social-link" title="تويتر">
                    <i class="fab fa-twitter"></i>
                </a>
                <a href="#" class="social-link" title="إنستغرام">
                    <i class="fab fa-instagram"></i>
                </a>
                <a href="#" class="social-link" title="لينكد إن">
                    <i class="fab fa-linkedin-in"></i>
                </a>
            </div>
        </div>
    </div>

    <script>
        // إنشاء الجسيمات المتحركة
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                const size = Math.random() * 5 + 2;
                const x = Math.random() * window.innerWidth;
                const y = Math.random() * window.innerHeight;
                const delay = Math.random() * 6;
                
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.animationDelay = delay + 's';
                
                particlesContainer.appendChild(particle);
            }
        }

        // العد التنازلي
        function startCountdown() {
            const launchDate = new Date();
            launchDate.setDate(launchDate.getDate() + 20);
            
            function updateCountdown() {
                const now = new Date().getTime();
                const distance = launchDate.getTime() - now;
                
                if (distance < 0) {
                    document.getElementById('countdown').innerHTML = '<h2 style="color: white;">تم الإطلاق!</h2>';
                    return;
                }
                
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                
                document.getElementById('days').textContent = days.toString().padStart(2, '0');
                document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
            }
            
            updateCountdown();
            setInterval(updateCountdown, 1000);
        }

        // اشتراك البريد الإلكتروني
        function subscribeEmail() {
            const email = document.querySelector('.email-input').value;
            if (email && email.includes('@')) {
                alert('شكراً لك! سنرسل لك إشعاراً عند إطلاق الموقع.');
                document.querySelector('.email-input').value = '';
            } else {
                alert('يرجى إدخال بريد إلكتروني صحيح.');
            }
        }

        // تشغيل الوظائف عند تحميل الصفحة
        window.addEventListener('load', function() {
            createParticles();
            startCountdown();
        });

        // إعادة إنشاء الجسيمات عند تغيير حجم النافذة
        window.addEventListener('resize', function() {
            document.getElementById('particles').innerHTML = '';
            createParticles();
        });
    </script>
</body>
</html>
